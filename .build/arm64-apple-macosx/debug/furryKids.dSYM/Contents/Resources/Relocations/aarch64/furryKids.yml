---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids'
relocations:
  - { offset: 0x91, size: 0x8, addend: 0x0, symName: _furryKids_main, symObjAddr: 0x0, symBinAddr: 0x100003400, symSize: 0xD0 }
  - { offset: 0xA7, size: 0x8, addend: 0x0, symName: _furryKids_main, symObjAddr: 0x0, symBinAddr: 0x100003400, symSize: 0xD0 }
  - { offset: 0xC4, size: 0x8, addend: 0x0, symName: '_$sSa12_endMutationyyF', symObjAddr: 0x168, symBinAddr: 0x100003568, symSize: 0xC }
  - { offset: 0xE5, size: 0x8, addend: 0x0, symName: '_$ss27_finalizeUninitializedArrayySayxGABnlF', symObjAddr: 0xD0, symBinAddr: 0x1000034D0, symSize: 0x40 }
  - { offset: 0x10D, size: 0x8, addend: 0x0, symName: '_$ss5print_9separator10terminatoryypd_S2StFfA0_', symObjAddr: 0x110, symBinAddr: 0x100003510, symSize: 0x2C }
  - { offset: 0x128, size: 0x8, addend: 0x0, symName: '_$ss5print_9separator10terminatoryypd_S2StFfA1_', symObjAddr: 0x13C, symBinAddr: 0x10000353C, symSize: 0x2C }
...
