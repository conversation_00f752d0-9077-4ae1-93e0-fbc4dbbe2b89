/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/main.swift.o : /Users/<USER>/WorkSpace/furryKids/Sources/main.swift /Library/Developer/CommandLineTools/usr/lib/swift/host/plugins/libSwiftMacros.dylib /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface
