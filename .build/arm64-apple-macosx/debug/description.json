{"builtTestProducts": [], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.furryKids-arm64-apple-macosx15.0-debug.module": {"executable": "/Library/Developer/CommandLineTools/usr/bin/swiftc", "fileList": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/sources", "importPath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/Sources/main.swift"}, {"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt"}, {"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/sources"}], "isLibrary": false, "moduleName": "furryKids", "moduleOutputPath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/Modules/furryKids.swiftmodule", "objects": ["/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/main.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "furryKids_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "furrykids"], "outputFileMapPath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/Modules/furryKids.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/WorkSpace/furryKids/Sources/main.swift"], "tempsPath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"furryKids": ["/Library/Developer/CommandLineTools/usr/bin/swiftc", "-module-name", "furryKids", "-package-name", "furrykids", "-incremental", "-c", "/Users/<USER>/WorkSpace/furryKids/Sources/main.swift", "-I", "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx10.13", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "furryKids_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "furrykids", "-driver-use-frontend-path", "/Library/Developer/CommandLineTools/usr/bin/swiftc"]}, "targetDependencyMap": {"furryKids": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "writeCommands": {"/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids-entitlement.plist"}, "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/Sources/main.swift"}], "outputFilePath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/sources"}, "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.build/main.swift.o"}], "outputFilePath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/furryKids.product/Objects.LinkFileList"}, "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Library/Developer/CommandLineTools/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/WorkSpace/furryKids/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt"}}}