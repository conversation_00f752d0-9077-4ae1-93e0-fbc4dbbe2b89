<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-gray-50 justify-between group/design-root overflow-x-hidden"
      style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-gray-50 p-4 pb-2 justify-between">
          <div class="text-[#101618] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#101618] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Buddy</h2>
        </div>
        <div class="flex items-end gap-3 p-4">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBzpM7NkEsBiJdjbHyXQMpdvsL_Y-V5eIXDc7kn1B_tGY0GNmUnkOqZf1Kp25l0JUgWgYYdTP9fv_xizirfLgXUETKgCMSpU0Wcs6J6GS9KfGGdfYAQVVhLlCWQky2O96jciKNUy7nIwAvdefbv3YtfeGIU9xYmptv-eXoZvv6NqQUM_9i5rhlSR6C_iBzi0RGWpQVQ7_G1fhuuhH-DtX3Ebispm8-vENiPyM2xdK_nXkIZ1e5PJLS_o-n7pdL8pZqodZk47rzIAfs");'
          ></div>
          <div class="flex flex-1 flex-col gap-1 items-start">
            <p class="text-[#5c7d8a] text-[13px] font-normal leading-normal max-w-[360px]">Buddy</p>
            <p class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#eaeff1] text-[#101618]">
              Woof woof! I'm so excited to chat with you today! What's on your mind?
            </p>
          </div>
        </div>
        <div class="flex items-end gap-3 p-4 justify-end">
          <div class="flex flex-1 flex-col gap-1 items-end">
            <p class="text-[#5c7d8a] text-[13px] font-normal leading-normal max-w-[360px] text-right">Olivia</p>
            <p class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#dcedf3] text-[#101618]">
              Hey Buddy! I was wondering if you'd like to go for a walk later?
            </p>
          </div>
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuADCszP10l5UlWdB_73z4iEgrb7WjpqKF9OUQoQk1mCfLhoBp_TVCQx7UnkhRWkpEjS70gOXCSs_aJtuOYMQ97OaQovIgA2CheuAQ71dBnN3f1qnu6DUCUJnZeCff9vgWqh2pFlMLA_BeWoGv2f7uVTBc63GfN_wlXbLAT0cj7pQ0e1la3iVxVYk_Bbss0Gg0IMFAP6UkeimvRHhKLkLMiHzb-l-kl8uQlsN4C9G3W4zQWIBdbTH0Nrk5kEzfaz6eOqL9n10w1LlHE");'
          ></div>
        </div>
        <div class="flex items-end gap-3 p-4">
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
            style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB8qKyL28E6KFSyh45uNA5Ifyp7AvjLc2lgYoqVcUrCVSOIEq43-tf5QJrBvZamm05EEXIlbAPgRRyDI6rgSpxJi1G4FBTS4rW4zJ3yHSHM0L9QwVtzvpEws55MOMHGGJjQS3I2lO5-6XpGauUgjXMK0CgVYDL_A2rffm0pA_xial0ZZjI7piBFbEA1zmY5LGy4qDlccRn_Y2qf2ADqckBSrdQfqKkCXfQqkwFSKq5Mf1dhfImf9go88bWlvuUxYF8CgGJsHOc8f_Q");'
          ></div>
          <div class="flex flex-1 flex-col gap-1 items-start">
            <p class="text-[#5c7d8a] text-[13px] font-normal leading-normal max-w-[360px]">Buddy</p>
            <p class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#eaeff1] text-[#101618]">
              A walk? That sounds pawsome! I can't wait to explore the neighborhood with you.
            </p>
          </div>
        </div>
      </div>
      <div>
        <div class="flex items-center px-4 py-3 gap-3 @container">
          <label class="flex flex-col min-w-40 h-12 flex-1">
            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
              <input
                placeholder="Type a message..."
                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#101618] focus:outline-0 focus:ring-0 border-none bg-[#eaeff1] focus:border-none h-full placeholder:text-[#5c7d8a] px-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                value=""
              />
              <div class="flex border-none bg-[#eaeff1] items-center justify-center pr-4 rounded-r-xl border-l-0 !pr-2">
                <div class="flex items-center gap-4 justify-end">
                  <div class="flex items-center gap-1">
                    <button class="flex items-center justify-center p-1.5">
                      <div class="text-[#5c7d8a]" data-icon="Image" data-size="20px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                          <path
                            d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,16V158.75l-26.07-26.06a16,16,0,0,0-22.63,0l-20,20-44-44a16,16,0,0,0-22.62,0L40,149.37V56ZM40,172l52-52,80,80H40Zm176,28H194.63l-36-36,20-20L216,181.38V200ZM144,100a12,12,0,1,1,12,12A12,12,0,0,1,144,100Z"
                          ></path>
                        </svg>
                      </div>
                    </button>
                  </div>
                  <button
                    class="min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#dcedf3] text-[#101618] text-sm font-medium leading-normal hidden @[480px]:block"
                  >
                    <span class="truncate">Send</span>
                  </button>
                </div>
              </div>
            </div>
          </label>
        </div>
        <div class="flex gap-2 border-t border-[#eaeff1] bg-gray-50 px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#5c7d8a]" href="#">
            <div class="text-[#5c7d8a] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#5c7d8a] text-xs font-medium leading-normal tracking-[0.015em]">Home</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#5c7d8a]" href="#">
            <div class="text-[#5c7d8a] flex h-8 items-center justify-center" data-icon="Share" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M229.66,109.66l-48,48a8,8,0,0,1-11.32-11.32L204.69,112H165a88,88,0,0,0-85.23,66,8,8,0,0,1-15.5-4A103.94,103.94,0,0,1,165,96h39.71L170.34,61.66a8,8,0,0,1,11.32-11.32l48,48A8,8,0,0,1,229.66,109.66ZM192,208H40V88a8,8,0,0,0-16,0V208a16,16,0,0,0,16,16H192a8,8,0,0,0,0-16Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#5c7d8a] text-xs font-medium leading-normal tracking-[0.015em]">Share</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#101618]" href="#">
            <div class="text-[#101618] flex h-8 items-center justify-center" data-icon="ChatCircleDots" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M128,24A104,104,0,0,0,36.18,176.88L24.83,210.93a16,16,0,0,0,20.24,20.24l34.05-11.35A104,104,0,1,0,128,24ZM84,140a12,12,0,1,1,12-12A12,12,0,0,1,84,140Zm44,0a12,12,0,1,1,12-12A12,12,0,0,1,128,140Zm44,0a12,12,0,1,1,12-12A12,12,0,0,1,172,140Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#101618] text-xs font-medium leading-normal tracking-[0.015em]">Interaction</p>
          </a>
        </div>
        <div class="h-5 bg-gray-50"></div>
      </div>
    </div>
  </body>
</html>
