import SwiftUI
import Foundation
import Combine
import AVFoundation



// MARK: - 旧的Store定义已移除，现在使用 /Stores/ 目录下集成了后端API的新Store

// 旧的FeedStore定义已移除

// MARK: - AuthStore定义已移除，使用 /Stores/AuthStore.swift

// MARK: - 临时 LoginView (直到项目文件包含正确的 LoginView)
struct LoginView: View {
    @EnvironmentObject var authStore: AuthStore
    @State private var username = ""
    @State private var password = ""

    var body: some View {
        VStack(spacing: 30) {
            Text("FurryKids")
                .font(.largeTitle)
                .fontWeight(.bold)

            VStack(spacing: 16) {
                TextField("用户名", text: $username)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                SecureField("密码", text: $password)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                <PERSON><PERSON>("登录") {
                    Task {
                        await authStore.login(username: username, password: password)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                .disabled(username.isEmpty || password.isEmpty)
            }
            .padding(.horizontal, 40)

            Spacer()
        }
        .padding(.top, 100)
    }
}

// MARK: - 临时 UserProfileView
struct UserProfileView: View {
    @EnvironmentObject var authStore: AuthStore

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("我的")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("用户: \(authStore.currentUser?.username ?? "未知")")
                    .font(.headline)

                Button("退出登录") {
                    authStore.logout()
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(10)

                Spacer()
            }
            .padding()
        }
    }
}

// MARK: - 主应用

@main
struct FurryKidsApp: App {
    @StateObject private var authStore = AuthStore()

    var body: some Scene {
        WindowGroup {
            if authStore.isLoading {
                // 启动画面
                SplashView()
            } else if authStore.isAuthenticated {
                // 已登录，显示主应用
                MainContentView()
                    .environmentObject(authStore)
            } else {
                // 未登录，显示登录界面
                LoginView()
                    .environmentObject(authStore)
            }
        }
    }
}

// MARK: - 启动画面
struct SplashView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "pawprint.circle.fill")
                .font(.system(size: 100))
                .foregroundColor(.blue)

            Text("毛孩子AI")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(Color(hex: "#101618"))

            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .blue))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(hex: "#F9FAFB"))
    }
}

// MARK: - 主内容视图
struct MainContentView: View {
    @EnvironmentObject var authStore: AuthStore
    // 使用集成了后端API的新Store
    @StateObject private var petStore = PetStore()
    @StateObject private var feedStore = FeedStore()
    @StateObject private var interactionStore = InteractionStore()

    var body: some View {
        TabView {
            FeedView()
                .environmentObject(feedStore)
                .environmentObject(petStore)
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("首页")
                }

            InteractionView()
                .environmentObject(interactionStore)
                .environmentObject(petStore)
                .tabItem {
                    Image(systemName: "message.fill")
                    Text("聊天")
                }

            ProfileView()
                .environmentObject(petStore)
                .tabItem {
                    Image(systemName: "pawprint.fill")
                    Text("宠物")
                }

            UserProfileView()
                .environmentObject(authStore)
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("我的")
                }
        }
        .accentColor(.blue)
    }
}



