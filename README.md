# 毛孩子AI - iOS 宠物社交应用

## 项目介绍

毛孩子AI是一款专为宠物主人设计的iOS社交应用，让用户可以为自己的宠物创建个性化AI形象，与其互动聊天，并通过社区功能与其他宠物主人分享日常。

## 功能特点

- **宠物资料**: 创建和管理宠物的个性化资料
- **社区动态**: 发布和浏览宠物动态、照片等内容
- **互动聊天**: 与AI宠物进行智能对话
- **个性成长**: 宠物随着互动提升等级和经验

## 技术栈

- SwiftUI
- Combine
- iOS 15+

## 运行指南

1. 确保已安装Xcode 13或更高版本
2. 克隆项目到本地
3. 打开FurryKidsAI.xcodeproj
4. 选择一个iOS模拟器或实体设备
5. 点击运行按钮

## 项目结构

```
FurryKidsAI/
├── Models/         # 数据模型
├── Views/          # UI视图
├── Utilities/      # 工具类和扩展
└── FurryKidsAIApp.swift  # 应用入口
```

## 联系方式

如有任何问题或建议，请联系开发团队。

## 版权信息

© 2023 毛孩子AI开发团队，保留所有权利。