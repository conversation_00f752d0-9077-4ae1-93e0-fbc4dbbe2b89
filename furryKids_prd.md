# 毛孩子AI - iOS 原生应用产品需求文档（PRD）

**版本**：v1.0  
**平台**：iOS（iPhone 优先适配）  
**开发技术**：SwiftUI + Combine + AVFoundation + Socket.IO  
**文档更新日期**：2025-06-08  
**编写人**：产研负责人

---

## 一、项目概述

毛孩子AI是一款面向宠物主人的AI互动陪伴应用，通过拟人化AI驱动技术，赋予宠物“表达”、“互动”、“社交”的能力，打造有温度的数字宠物社交平台。该应用专为iOS平台设计，强调原生流畅性与极致互动体验。

---

## 二、产品定位与目标

| 目标 | 说明 |
|------|------|
| 拟人宠物形象 | 宠物具备独特的性格、语气、表达方式 |
| 主宠高频互动 | 主人可随时与宠物语音或文字交流 |
| 宠物社交分享 | 宠物之间可点赞评论，构建“宠物朋友圈” |

---

## 三、目标用户画像

- **95后城市年轻宠物主人**
- 喜欢表达、注重陪伴感
- 爱用AI、有轻社交需求
- 热衷晒宠物、注重情绪陪伴

---

## 四、核心功能模块

### 1. 宠物主页（Profile）

| 功能 | 描述 |
|------|------|
| 宠物基础信息展示 | 头像、名字、性别、年龄、品种、个性签名 |
| 情绪状态与活动轨迹 | 显示今日心情（AI生成）、活动记录（可模拟或接入Apple Health） |
| 编辑功能 | 支持修改头像、签名、性格标签等 |

### 2. 动态分享页（Feed）

| 功能 | 描述 |
|------|------|
| 宠物发布动态 | 图片/语录/心情等动态，AI辅助生成文案 |
| 动态浏览 | 展示他宠动态流，可按品种/标签筛选 |
| 评论点赞 | 可对动态进行评论、点赞、互动表情 |
| 动态发布器 | 图文编辑，添加心情标签和宠物语气 |

### 3. 互动页（Interaction）

| 功能 | 描述 |
|------|------|
| 宠物AI对话 | 主人输入文字/语音，宠物基于AI进行拟人回复 |
| 语音合成播报 | 宠物回复通过TTS语音模拟播出（AVSpeechSynthesizer） |
| 表情反馈 | 宠物表情卡通化渲染，支持动态贴纸动画 |
| 互动命令 | 点按按钮执行互动动作：“打滚”、“卖萌”、“飞吻”等 |

---

## 五、技术架构（iOS原生）

### 1. 客户端

| 模块 | 技术栈 |
|------|--------|
| UI层 | SwiftUI |
| 状态管理 | Combine + `@StateObject` |
| 网络通信 | URLSession / Alamofire |
| 实时通信 | Socket.IO Client (Starscream wrapper) |
| 本地语音播报 | AVFoundation (TTS) |
| 图片缓存 | Kingfisher 或 AsyncImage（iOS 15+） |

### 2. 后端服务（复用Web架构）

| 项目 | 技术 |
|------|------|
| 接口服务 | Node.js + Express |
| 数据存储 | MongoDB / MySQL |
| AI服务 | OpenAI GPT / 自研对话模型 |
| 文件服务 | S3 / CDN 上传宠物图片与音频 |

---

## 六、交互设计概览（建议配合 Figma 原型）

### 主导航结构（底部Tab）

- 🐾 Feed｜🐶 Profile｜💬 Interaction

### Profile 页

- 顶部宠物头像与心情状态
- 中部：成长记录与互动历史
- 底部：编辑入口与快捷指令

### Feed 页

- 滑动动态流
- 发布按钮浮动在右下角
- 点进动态：支持评论 + 表情回应

### Interaction 页

- 对话气泡区（宠物左 / 主人右）
- 下方输入框 + 表情快捷按钮
- 宠物AI回复自动语音播放

---

## 七、数据结构设计（简略）

### 宠物结构 Pet

```json
{
  "id": "pet001",
  "name": "苹果",
  "avatar": "https://cdn.xxx/pet001.jpg",
  "breed": "金毛",
  "age": 3,
  "mood": "开心",
  "personality": ["粘人", "活泼"]
}

